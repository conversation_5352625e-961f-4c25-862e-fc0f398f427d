#!/usr/bin/env python3
"""
测试字段映射功能
验证 map_doc_result 函数是否正确进行字段转换和筛选
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_mapper import map_doc_result, get_doc_sql_fields, DOC_FIELD_MAPPING, DOC_TARGET_FIELDS

def test_field_mapping():
    """测试字段映射功能"""
    print("=== 测试字段映射功能 ===\n")
    
    # 1. 测试 get_doc_sql_fields() 函数
    print("1. 测试 get_doc_sql_fields() 函数:")
    sql_fields = get_doc_sql_fields()
    print(f"SQL字段列表: {sql_fields}")
    print(f"字段数量: {len(sql_fields.split(', '))}")
    print()
    
    # 2. 显示字段映射配置
    print("2. 字段映射配置:")
    for mysql_field, target_field in DOC_FIELD_MAPPING.items():
        print(f"  {mysql_field} -> {target_field}")
    print()
    
    # 3. 显示目标字段列表
    print("3. 目标字段列表:")
    for i, field in enumerate(DOC_TARGET_FIELDS, 1):
        print(f"  {i:2d}. {field}")
    print()
    
    # 4. 模拟MySQL查询结果（DictCursor格式）
    print("4. 测试字段转换:")
    mock_mysql_result = {
        'id': 'doc_001',
        'original_id': 'orig_001',
        'title': '测试文档标题',
        'agent_id': 'agent_001',
        'dir_id': 'dir_001',
        'pub_user_name': '张三',
        'pub_time': '2023-07-05 19:54:24',
        'version': '1.0',
        'tags': 'tag1,tag2',
        'attaches': 'file1.pdf,file2.doc',
        'word_num': 1500,
        'picture_num': 3,
        'link_num': 2,
        'plain_text': '这是文档的纯文本内容...',
        'rich_text': '<p>这是文档的HTML内容...</p>',
        'version_status': 1,
        # 额外的字段（不在映射中，应该被忽略）
        'sys_created': '2023-07-01 10:00:00',
        'sys_updated': '2023-07-05 19:54:24',
        'delete_flag': 0,
        'extra_field': 'should_be_ignored'
    }
    
    print("原始MySQL查询结果（部分字段）:")
    key_fields = ['id', 'agent_id', 'title', 'plain_text', 'rich_text', 'pub_user_name', 'version_status']
    for field in key_fields:
        if field in mock_mysql_result:
            value = mock_mysql_result[field]
            if isinstance(value, str) and len(value) > 30:
                value = value[:30] + "..."
            print(f"  {field}: {value}")
    print()
    
    # 5. 执行字段映射
    print("5. 执行字段映射:")
    mapped_result = map_doc_result(mock_mysql_result)
    
    print("映射后的结果（核心字段）:")
    key_mapped_fields = ['id', 'agentId', 'title', 'docText', 'docHtml', 'pubUserName', 'versionStatus']
    for field in key_mapped_fields:
        if field in mapped_result:
            value = mapped_result[field]
            if isinstance(value, str) and len(value) > 30:
                value = value[:30] + "..."
            print(f"  {field}: {value}")
    print()
    
    # 6. 验证字段转换
    print("6. 验证字段转换:")
    conversions = [
        ('agent_id', 'agentId'),
        ('plain_text', 'docText'),
        ('rich_text', 'docHtml'),
        ('pub_user_name', 'pubUserName'),
        ('version_status', 'versionStatus')
    ]
    
    for mysql_field, target_field in conversions:
        original_value = mock_mysql_result.get(mysql_field)
        mapped_value = mapped_result.get(target_field)
        status = "✓" if original_value == mapped_value else "✗"
        print(f"  {status} {mysql_field} -> {target_field}: {original_value} -> {mapped_value}")
    print()
    
    # 7. 验证字段筛选
    print("7. 验证字段筛选:")
    print(f"原始结果字段数: {len(mock_mysql_result)}")
    print(f"映射后字段数: {len(mapped_result)}")
    print(f"目标字段数: {len(DOC_TARGET_FIELDS)}")
    
    # 检查是否包含所有目标字段
    missing_fields = []
    for field in DOC_TARGET_FIELDS:
        if field not in mapped_result:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"缺失的目标字段: {missing_fields}")
    else:
        print("✓ 包含所有目标字段")
    
    # 检查是否有多余字段
    extra_fields = []
    for field in mapped_result:
        if field not in DOC_TARGET_FIELDS:
            extra_fields.append(field)
    
    if extra_fields:
        print(f"多余的字段: {extra_fields}")
    else:
        print("✓ 没有多余字段")
    print()
    
    # 8. 测试列表格式
    print("8. 测试列表格式:")
    mock_list_result = [mock_mysql_result, mock_mysql_result.copy()]
    mapped_list_result = map_doc_result(mock_list_result)
    
    print(f"原始列表长度: {len(mock_list_result)}")
    print(f"映射后列表长度: {len(mapped_list_result)}")
    print(f"映射后第一条记录的字段数: {len(mapped_list_result[0])}")
    print()

if __name__ == "__main__":
    test_field_mapping()
