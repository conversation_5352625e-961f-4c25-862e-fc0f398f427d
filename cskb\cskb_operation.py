import re
import fitz
import json
import datetime
import requests
from bs4 import BeautifulSoup
from tqdm import tqdm

import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.mysql import MySQLDatabase
from config import KNOWLEDGE_FIELD_CSKB_ENV, CSKB_ADDRESS_DICT, KNOWLEDGE_FIELDS
from env import ENVIRONMENT
from utils.data_mapper import map_doc_result, map_faq_result, get_doc_sql_fields, get_faq_sql_fields, FAQ_TARGET_FIELDS, DOC_TARGET_FIELDS



class CSKBOperation:
    def __init__(self, db: str = 'cskb'):
        self.db = MySQLDatabase(db)
        self.env = ENVIRONMENT
        # self.cskb_agents_info = self.get_cskb_agents_info()
        self.all_dir_tree_dict = {}

    # def get_cskb_agents_info(self) -> list:
    #     """获取CSKB知识库的agent_id和access_token"""
    #     sql = f'SELECT * FROM cskb_agent_key where env = \'{self.env}\''
    #     results = self.db.execute_query(sql)
    #     return results

    # 查询知识库文档具体内容
    def get_agent_doc_detail(self, agent, doc_id):
        access_token = agent['access_token']
        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
        }        
        
        cskb_url = CSKB_ADDRESS_DICT[self.env]['cskb_url']
        
        doc_detail_url = f"{cskb_url}/cskb/open/api/v1/doc/detail?id={doc_id}"

        response = requests.get(doc_detail_url, headers=headers)

        answer_json = response.json()

        if "data" not in answer_json.keys():
            return None

        data = answer_json['data']
        html_answer = data['richText']

        # 使用BeautifulSoup解析HTML文档
        soup = BeautifulSoup(html_answer, 'html.parser')

        # 定位要删除的DOM节点
        [s.extract() for s in soup("title")]
        text_answer = soup.get_text().strip()

        agent_name = agent['agent_name']

        doc_info = {
            "id": data['id'],
            "id_parent": "",
            "title": data['title'],
            "agentId": data['agentId'],
            "agentName": agent_name,
            "dirId": data['dirId'],
            "originalId": data['originalId'],
            "originalId_parent": "",
            "pubUserName": data['pubUserName'],
            "pubTime": data['pubTime'],
            "version": data['version'],
            "tags": data['tags'],
            "attaches": data['attaches'],
            "wordNum": data['wordNum'],
            "pictureNum": data['pictureNum'],
            "linkNum": data['linkNum'],
            "docText": text_answer,
            "docHtml": html_answer,
        }

        return doc_info

    # 读取附件
    def get_agent_doc_attach_detail(self, agent, doc_title, attach_title, attach_id):
        access_token = agent['access_token']
        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
        } 
        cskb_attach_download_url = CSKB_ADDRESS_DICT[self.env]['cskb_attach_download_url']

        attach_content = f"BM/询价单名称：{doc_title}，附件名称：{attach_title}，附件内容如下：\n\n"
        
        attach_download_url = f"{cskb_attach_download_url}/cskb/storage/v1/download"
        param = {
            "id":attach_id,
            "preview": "true"
        }

        try:
            response = requests.get(attach_download_url, headers=headers, params=param)
        except Exception as error:
            print(f"附件{attach_id}下载失败: {error}")
            return ""
        finally:
            pass
        
        if response.status_code == 200:
            # 获取内容类型
            content_type = response.headers.get('Content-Type')

            # 处理 PDF 响应
            if 'application/pdf' in content_type:
                try:
                    pdf = fitz.open(stream=response.content, filetype="pdf")  # type: ignore

                    for page in pdf:
                        text = page.get_text(sort=True)
                        text = re.sub(r"\s*\n\s*", "\n", text)
                        # @TODO:超过10000字符的部分不读取
                        if len(attach_content) > 1000000:
                            break
                        attach_content += text.strip()
                except Exception as error:
                    print(f"附件{attach_id}读取失败: {error}")

                finally:
                    pass
                
            # 处理图片响应
            elif 'image/' in content_type:
                # 暂时不对图片文件进行处理
                pass

            else:
                print(f"附件id:{attach_id}, 响应内容不是 PDF 或图片")
        else:
            print(f"附件id:{attach_id}, 请求失败，状态码: {response.status_code}")

        return attach_content


    def get_agent_docs_and_attaches(self, agent):
        """根据文档id查询文档内容"""

        print('开始获取文档和附件')

        docs = self.get_agent_all_doc_ids(agent)

        knowledge_list = []
        # 获取Agent目录清单
        dir_tree_dict = self.get_directory_tree(agent)

        # 初始化带进度条的迭代器
        progress = tqdm(
            docs,
            desc=f"处理 {agent['agent_name']}",
            bar_format="{l_bar}{bar:20}{r_bar}",
            ncols=100,  # 控制进度条总宽度
            mininterval=0.5  # 更新频率
        )

        for i, doc in tqdm(enumerate(progress, 1)):
            
            doc_id = doc['id']
            doc_detail = self.get_agent_doc_detail(agent=agent, doc_id=doc_id)
            if not doc_detail:
                continue

            doc_text = doc_detail['docText']
            doc_html = doc_detail['docHtml']
            doc_attaches = doc_detail['attaches']
            
            if not doc_text and not doc_html and doc_attaches:
                return []
            
            # 将doc_detail的内容更新到doc中
            doc.update(doc_detail)

            doc_dir_id = doc['dirId']

            # 获取完整目录名称和目录层级
            doc['dir_name'] = f"[{doc['agentName']}] {dir_tree_dict[doc_dir_id]['full_name']}"
            doc['dir_level'] = dir_tree_dict[doc_dir_id]['level']
            
            knowledge_list.append({"type": "doc", "data": doc})

            if doc_attaches:
                for attach in doc_attaches:

                    # 附件文档中部分key，用其他key值替换
                    attach['originalId'] = attach['id']
                    attach['title'] = attach['name']

                    # 附件的基本信息和其关联主文档保持一致，TODO：今后若要区分层级再考虑修改此部分代码
                    attach['agentId'] = doc['agentId']
                    attach['agentName'] = doc['agentName']
                    attach['dirId'] = doc['dirId']
                    attach['dir_name'] = doc['dir_name']
                    attach['dir_level'] = doc['dir_level']

                    attach_text = self.get_agent_doc_attach_detail(agent=agent, doc_title=doc['title'], attach_title = attach['title'], attach_id=attach['previewId'])
                    attach['docText'] = attach_text
                    attach["id_parent"] = doc['id']
                    attach['originalId_parent'] = doc['originalId']

                    knowledge_list.append({"type": "attach", "data": attach})
            
            break

        return knowledge_list


    # 获取所有文档
    def get_agent_all_doc_ids(self, agent):
        access_token = agent['access_token']
        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
        }
        cskb_url = CSKB_ADDRESS_DICT[self.env]['cskb_url']

        finish_fetching = False
        current_round = 0
        max_round = 9999

        docs_list = []
        pn = 1 # 页码
        
        while (finish_fetching is False):
            doc_list_url = f"{cskb_url}/cskb/open/api/v1/doc?pn={pn}&ps=100"
            response = requests.get(doc_list_url, headers=headers)
            answer_json = response.json() 

            if "data" not in answer_json.keys():
                continue

            answer = answer_json['data']['list']

            for ans in answer:
                doc_info = {
                    "id": ans['id'],
                    "originalId": ans['originalId'],
                }
                docs_list.append(doc_info)

            current_round += 1
            pn += 1
            if (len(answer) == 0 or current_round > max_round):
                # 如果返回结果为空或者超过最大查询次数，则结束查询
                finish_fetching = True

        return docs_list

    def get_agent_all_knowledge(self, agent):
        docs_list = self.get_agent_docs_and_attaches(agent)
        faqs_list = self.get_agent_all_faqs(agent)
        knowledge_list = docs_list + faqs_list
        return knowledge_list

    # 获取所有FAQ
    def get_agent_all_faqs(self, agent):
        
        print('开始获取faq')
        
        access_token = agent['access_token']
        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
        }
        cskb_url = CSKB_ADDRESS_DICT[self.env]['cskb_url']

        faq_list = []
    
        # 获取Agent目录清单
        dir_tree_dict = self.get_directory_tree(agent)

        finish_fetching = False
        current_round = 0
        max_round = 999

        pn = 1 # 页码

        while (finish_fetching is False):
            standard_faq_url = f"{cskb_url}/cskb/open/api/v1/faq/standard?pn={pn}&ps=100"

            response = requests.get(standard_faq_url, headers=headers)
            answer_json = response.json()   
            
            if "data" not in answer_json.keys():
                continue

            answer = answer_json['data']['list']

            for ans in answer:
                agent_id = ans['agentId']
                agent_name = agent['agent_name']

                question_text = ans['question']
                answer_json = json.loads(ans['answer'])['list'][0]
                if answer_json['type'] == 3:
                    answer_text_html = answer_json['text']
                    answer_text = re.sub('<.*?>', '', answer_text_html).strip()
                else:
                    answer_text = answer_json['text']

                faq_info = {
                    "id": ans['id'],
                    "agentId": agent_id,
                    "agentName": agent_name,
                    "question": question_text,
                    "answer": answer_text,
                    "dirId": ans['dirId'],
                    "originalId": ans['originalId'],
                    "pubUserName": ans['pubUserName'],
                    "pubTime": ans['pubTime'],
                    "versionStatus": ans['versionStatus'],
                }

                faq_dir_id = faq_info['dirId']
                faq_info['dir_name'] = f"[{faq_info['agentName']}] {dir_tree_dict[faq_dir_id]['full_name']}"
                faq_info['dir_level'] = dir_tree_dict[faq_dir_id]['level']

                faq_list.append({"type": "faq", "data": faq_info})

            current_round += 1
            pn += 1
            if (len(answer) == 0 or current_round > max_round):
                # 如果返回结果为空或者超过最大查询次数，则结束查询
                finish_fetching = True

            break

        return faq_list


    # 获取目录结构
    def get_directory_tree(self, agent):
        access_token = agent['access_token']

        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
        }
        
        cskb_url = CSKB_ADDRESS_DICT[self.env]['cskb_url']

        answer_dict = {}

        dir_tree_url = f"{cskb_url}/cskb/open/api/v1/directory?type=1"

        response = requests.get(dir_tree_url, headers=headers)

        answer = response.json()
        if 'data' not in answer.keys():
            return answer_dict

        answer = answer['data']

        # 将answer中完整目录树List转化成Dictionary，Key为dir_id，Value为完整目录名
        ## 第一步：创建一个临时Dictionary，Key为dir_id，Value为{底层目录名称，父目录ID，完整目录名称（由第二步逐层补充完整）}
        temp_dir_dict = {}
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_parent_id = dir['parentId']
            temp_dir_dict[dir_id] = {"name": dir_name, "parent_id": dir_parent_id, "full_dir_name": dir_name}
        ## 第二步：逐层补充完整目录名称，并保存到一个global Dictionary中，Key为dir_id，Value为完整目录名称
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_level = dir['level']
            dir_parent_id = dir['parentId']
            dir_full_name = dir_name
            while (dir_level > 1):
                dir_parent_name = temp_dir_dict[dir_parent_id]['full_dir_name']
                dir_full_name = f"{dir_parent_name}/" + dir_full_name
                dir_parent_id = temp_dir_dict[dir_parent_id]['parent_id']
                dir_level -= 1
            
            answer_dict[dir_id] = {"name": dir_name, "full_name": dir_full_name, "level": dir['level']}
        
        return answer_dict
            

    def get_cskb_incremental_knowledge(self, agent_id: str, query_date: str, knowledge_type: str) -> list:
        """"根据agent_id和时间获取更新的CSKB知识"""
        # 确定表名和字段列表
        if knowledge_type == 'doc':
            table = 'cskb_doc_pub'
            fields = get_doc_sql_fields()  # 获取需要的字段列表
        elif knowledge_type == 'faq':
            table = 'cskb_faq_pub'
            fields = get_faq_sql_fields()  # 获取需要的字段列表
        else:
            raise ValueError(f"不支持的知识类型: {knowledge_type}")

        # 处理查询日期
        if not query_date:
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            query_date = yesterday.strftime('%Y-%m-%d')
        else:
            query_date = datetime.datetime.strptime(query_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

        # 使用参数化查询，只查询需要的字段
        sql = f'SELECT {fields} FROM {table} WHERE agent_id = %s AND version_status = 1 AND DATE(sys_updated) = %s LIMIT 1'
        results = self.db.execute_query(sql, (agent_id, query_date))

        # 映射查询结果
        mapped_results = []
        if results:
            if knowledge_type == 'doc':
                mapped_result = map_doc_result(results)
            elif knowledge_type == 'faq':
                mapped_result = map_faq_result(results)
            else:
                mapped_result = []

            # 确保返回列表格式
            if isinstance(mapped_result, list):
                mapped_results = mapped_result
            elif mapped_result:
                mapped_results = [mapped_result]

        return mapped_results
        
    def complement_cskb_incremental_results(self, knowledge_list: list, knowledge_type: str):
        """格式化增量知识查询结果"""
        formatted_results = []

        if knowledge_type == 'doc':
            for knowledge in knowledge_list:
                print(knowledge)
                break
        

        


        

if __name__ == "__main__":
    db = 'cskb'
    cskb_operation = CSKBOperation(db=db)
    agent_id = '007c529739690a861ad158e4237fea06'
    query_date = '2023-07-05 19:54:24'
    knowledge_type = 'faq'
    results = cskb_operation.get_cskb_incremental_knowledge(agent_id=agent_id, query_date=query_date, knowledge_type=knowledge_type)
    # print(results)
    results = cskb_operation.complement_cskb_incremental_results(results, knowledge_type=knowledge_type)

    # results = cskb_operation.get_cskb_agents_info()
    # agent = {'id': 1, 'agent_id': 'ba96968f61b6b72a6ef97c87ab99bee6', 'agent_name': '零号员工知识库', 'access_token': '99f747e3-687b-4a5f-81c3-51513ea192a5', 'env': 'prod'}
    # agent = {'id': 5, 'agent_id': '007c529739690a861ad158e4237fea06', 'agent_name': '上汽大众客服知识库', 'access_token': '224ee926-559d-4435-b95a-098076e67353', 'env': 'prod'}
    # results = cskb_operation.get_agent_all_faqs(agent=agent)
    # doc_id = "c1_4b314f369693207781563a91659f76b8"
    # results = cskb_operation.get_agent_doc_detail(agent=agent,doc_id=doc_id)
    # docs = cskb_operation.get_agent_all_doc_ids(agent)
    # results = cskb_operation.get_agent_all_knowledge(agent)
    # print(results)

    